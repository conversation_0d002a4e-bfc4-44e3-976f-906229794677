<!--  
- Author        : <PERSON><PERSON> (Accenture) SFP-32835
- Date          : August 13, 2020
- Description   : CDR Data Sharing Arrangement page
-->
<template> 
    <div class="content__body"> 
        <br/>
        <!-- loading -->
        <template if:false={loaded}>
            <lightning-spinner class="loading slds-is-relative" alternative-text="Loading" size="large" style="position: fixed;"></lightning-spinner>
        </template>
        <template if:true={loaded}>
            <c-cdr-header back-btn-relative-url={backBtnRelativeUrl} business-acc-id={ownerAccId} back-enabled="true"></c-cdr-header>
            <template if:true={isRecordFound}>
                <div>
                    <template if:true={handledStopSharing}>
                        <div data-id="toastModal" class="slds-show slds-p-top_large "><!--style="height:6rem"-->
                            <div class="slds-notify_container slds-is-relative">
                                <div class="content__body-main-dashboard slds-notify_toast slds-theme_success slds-media slds-var-p-left_large slds-var-p-right_large toast-margin" role="status">
                                    <span class="slds-assistive-text">success</span>
                                    <span class="slds-icon_container slds-icon-utility-success slds-m-right_small slds-no-flex slds-align-top" title="Success">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="svg-check" aria-label="green check" alt="green check">;
                                                            <use xlink:href={svgURL}></use>
                                        </svg>
                                    </span>
                                    <div class="slds-notify__content">
                                        <h6 class="toast-message slds-text-heading_small font-style_light-bold ds-text-font-style"> You have successfully {messageString} with {recipientName}. </h6>
                                    </div>
                                    <div class="slds-icon_container slds-notify__close slds-icon-text-success">
                                        <button class="slds-button slds-button_icon" title="Close" onclick={closeToast} aria-label="cross graphic">
                                            <lightning-icon icon-name="utility:close" size="small" alternative-text="Close" title="Close"></lightning-icon>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div> 
                    </template>
                    <br/>
                    <h2 class="slds-text-align_left header-spacing font-style_light-bold" style="position: relative;">View your consent</h2>
                    <template if:true={withdrawn}>
                        <p class="ds-text-font-style_small text-gray float_none header-spacing slds-p-top_none">{recipientName} are no longer collecting and using your data for this product.</p>
                    </template>
                </div>
                <!-- Data recipient information -->
                <fieldset class="content__body-main-dashboard">
                    <legend class={legend_color}><p>{status}</p></legend>
                        <div class="slds-media slds-var-p-left_large slds-var-p-right_large slds-var-p-bottom_large slds-var-p-top_small">
                            <div class="slds-media__figure slds-media__figure_fixed-width slds-align_absolute-center slds-var-p-top_xx-small">
                                <div class="">
                                    <div class="slds-welcome-mat__tile-icon-container">
                                        <span class="slds-icon_container">
                                            <div class="slds-icon slds-icon-text-default">
                                                <img src={recipientLogo} class="arrangement-image" alt="company logo">
                                            </div>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="slds-media__body slds-grid slds-grid_vertical-align-center slds-var-p-top_xx-small">
                                <div class="slds-col">
                                    <h4 class="inline-text">{recipientName}</h4>
                                    <!-- SFP-41107. Receipt generation removed
                                    <a href={receiptUrl} download>
                                        <p class="consent-granted-text text-gray slds-text-link_faux slds-var-p-top_x-small">
                                            <lightning-icon icon-name="utility:download" alternative-text="Download" title="Download" size="xx-small"></lightning-icon>
                                            &nbsp;Confirmation of consent (pdf)
                                        </p>
                                    </a>
                                    -->
                                    <p class="slds-text-align_left text-gray consent-owner-text"> 
                                        Consent granted by <span class="consent-owner">{consentOwner}</span>:&nbsp;{startDate}
                                    </p>
                                </div>
                            </div>
                        </div>
                </fieldset>
                <!-- Data sharing information -->
                <template if:true={active}>
                    <!-- <h3 class="slds-text-align_left header-spacing font-style_light-bold">Data we're sharing</h3> -->
                    <h3 class="slds-text-align_left header-spacing font-style_light-bold data-shared-header">What you've shared</h3>
                </template>
                <template if:false={active}>
                    <h3 class="slds-text-align_left header-spacing font-style_light-bold data-shared-header">Data we've shared</h3>
                </template>
                <div class="">
                    <template for:each={dataClusterToPermLangMap} for:item="mapKey">
                            <ul class="" key={mapKey.key}>
                                <li class="slds-var-p-bottom_x-small">
                                    <c-cdr-data-shared-section key={mapKey.key} map-key={mapKey.key} value={mapKey.value} header={mapKey.header} body={mapKey.body} consent-start={startDate} consent-end={endDate} recipient-name={recipientName} status={onceoff}></c-cdr-data-shared-section>
                                </li>
                            </ul>
                    </template>
                </div>
                <br/>
                <!-- Key dates information -->
                <div class="content__body-main-dashboard-shadow slds-var-p-left_large slds-var-p-right_large slds-var-p-top_large slds-var-p-bottom_large slds-border_bottom slds-border_right slds-border_left">
                    <h4 class="inline-text">Key dates</h4>
                    <div>
                        <template if:true={onceoff}>
                            <div class="slds-grid slds-grid_vertical-align-center slds-var-p-top_medium">
                                <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3">
                                    <p class="text-gray">Consent given</p> <!-- SFP-62336 -->
                                </div>
                                <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                    <h6 class="slds-text-align_left slds-var-p-left_medium">{startDate}</h6>        
                                </div>
                            </div>
                            <div class="slds-grid slds-grid_vertical-align-center slds-var-p-top_medium">
                                <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3">
                                    <p class="text-gray">Consent expires</p>   <!-- SFP-62336 -->
                                </div>
                                <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                    <h6 class="slds-text-align_left slds-var-p-left_medium">{endDate}</h6>        
                                </div>
                            </div>
                            <div class="slds-grid slds-grid_vertical-align-center slds-var-p-top_medium">
                                <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3">
                                    <p class="text-gray">Sharing Period</p>
                                </div>
                                <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                    <h6 class="slds-text-align_left slds-var-p-left_medium">Your data will be shared once  </h6>        
                                </div>
                            </div>
                        </template>                      
                        <template if:false={onceoff}>
                            <div class="slds-col">
                                <div class="slds-grid slds-wrap slds-grid_vertical-align-center slds-var-p-top_medium">
                                    <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3">
                                        <p class="text-gray float_none">Consent given</p>  <!-- SFP-62336 -->
                                    </div>
                                    <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                        <h4 class="slds-text-align_left font-style_light-bold ds-text-font-style">{startDate}</h4>
                                    </div>
                                </div>
                            </div>
                            <template if:true={active}>
                                <div class="slds-col">
                                    <div class="slds-grid slds-wrap slds-grid_vertical-align-center slds-var-p-top_medium">
                                        <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3 slds-var-p-right_large">
                                            <p class="text-gray float_none">Consent expires</p>   <!-- SFP-62336 -->
                                        </div>
                                        <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                            <h4 class="slds-text-align_left font-style_light-bold ds-text-font-style">{endDate}</h4>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <template if:true={expired}>
                                <div class="slds-col">
                                    <div class="slds-grid slds-wrap slds-grid_vertical-align-center slds-var-p-top_medium">
                                        <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3">
                                            <p class="text-gray float_none">Consent expires</p>   <!-- SFP-62336 -->
                                        </div>
                                        <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                            <h4 class="slds-text-align_left font-style_light-bold ds-text-font-style">{endDate}</h4>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <template if:true={withdrawn}>
                                <div class="slds-col">
                                    <div class="slds-grid slds-wrap slds-grid_vertical-align-center slds-var-p-top_medium">
                                        <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3">
                                            <p class="text-gray float_none">When you withdrew your consent</p>
                                        </div>
                                        <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                            <h4 class="slds-text-align_left font-style_light-bold ds-text-font-style">{revocationDate}</h4>
                                        </div>
                                    </div>
                                </div>
                            </template>   
                            <div class="slds-grid slds-wrap slds-grid_vertical-align-center slds-var-p-top_medium">
                                <div class="slds-p-right_small slds-size_1-of-1 slds-medium-size_1-of-3 slds-large-size_1-of-3">
                                    <p class="text-gray float_none">Sharing period</p>
                                </div>
                                <div class="slds-size_1-of-1 slds-medium-size_2-of-3 slds-large-size_2-of-3">
                                    <template if:true={active}>
                                        <h4 class="slds-text-align_left font-style_light-bold ds-text-font-style">{startDate} - {endDate}</h4>
                                    </template>
                                    <template if:true={expired}>
                                        <h4 class="slds-text-align_left font-style_light-bold ds-text-font-style">{startDate} - {endDate}</h4>
                                    </template>
                                    <template if:true={withdrawn}>
                                        <h4 class="slds-text-align_left font-style_light-bold ds-text-font-style">{startDate} - {revocationDate}</h4>
                                    </template>                                
                                </div>
                            </div>                           
                        </template>
                    </div>
                </div>
                <!-- Financial Account information  -->
                <!--template if:true={showFinAccountInfo}-->
                <template if:true={showEditHeader}>
                    <div class="slds-text-align_left header-spacing slds-grid">
                        <div class="slds-col"> 
                            <h3>Accounts shared</h3>
                        </div>
                        <template if:true={isEditAccountVisible}>
                            <div class="slds-col"> 
                                <!-- Edit Accounts Button (SFP-34910) -->
                                <button class="slds-button slds-float_right" data-id={consentRecordId} onclick={handleEditAccounts}><p class="edit-label">Edit</p>
                                    <svg xmlns="http://www.w3.org/2000/svg" aria-label="Pencil graphic" viewBox="0 0 52 52" id="edit" class="icon-chevron-style">
                                        <path d="M9.5 33.4l8.9 8.9c.4.4 1 .4 1.4 0L42 20c.4-.4.4-1 0-1.4l-8.8-8.8c-.4-.4-1-.4-1.4 0L9.5 32.1c-.4.4-.4 1 0 1.3zM36.1 5.7c-.4.4-.4 1 0 1.4l8.8 8.8c.4.4 1 .4 1.4 0l2.5-2.5c1.6-1.5 1.6-3.9 0-5.5l-4.7-4.7c-1.6-1.6-4.1-1.6-5.7 0l-2.3 2.5zM2.1 48.2c-.2 1 .7 1.9 1.7 1.7l10.9-2.6c.4-.1.7-.3.9-.5l.2-.2c.2-.2.3-.9-.1-1.3l-9-9c-.4-.4-1.1-.3-1.3-.1l-.2.2c-.3.3-.4.6-.5.9L2.1 48.2z"></path>
                                    </svg>
                                </button>
                            </div>
                        </template>
                    </div>
                </template>
                <template if:true={active}>
                    <template if:true={showNonInitiatorAccountDisclaimer}>
                        <!-- <div class="slds-text-align_left header-spacing slds-grid">
                            <div class="slds-col"> 
                                <h3>Accounts shared</h3>
                            </div>
                        </div> -->
                        <template if:true={emptyFA}>
                            <div class="slds-text-align_left header-spacing slds-grid">
                                <div class="slds-col footer_consent-message "> 
                                    <p>Please note: Data from the account is no longer being shared with {recipientName}.</p>
                                </div>
                            </div>
                        </template>
                    </template>
                </template>
                <template if:true={showFinAccountInfo}>  <!--/template    COMMENT ME FOR TESTING ONLY  -->
                        <!-- End of SFP-34910 -->
                      <!-- SFP-55861 - added consent granted and ended dates in Accounts shared-->
                    <div class="content__body-main-dashboard-shadow slds-border_bottom slds-border_right slds-border_left accounts-list-box">
                        <div>  
                            <template iterator:it={financialAccDetails}>
                                <div key={it.value.id}>
                                    <lightning-layout class="slds-var-p-right_medium fin-acct-details slds-grid_vertical-align-center financial-accounts-padding">
                                        <lightning-layout-item flexibility="auto" padding="horizontal-none" class="slds-text-align_left slds-align-middle">
                                            <div class="slds-grid slds-wrap slds-grid_vertical-align-center">
                                                <div class="slds-col slds-size_6-of-12">
                                                    <template if:true={it.value.prodName}>
                                                    <h5 class="slds-text-align_left acc-name-label">{it.value.prodName}</h5>
                                                    </template>
                                                    <template if:false={it.value.prodName}>
                                                <h5 class="slds-text-align_left acc-name-label">{it.value.Financial_Account__r.Name}</h5>
                                                     </template>
                                                <p class="slds-text-align_left text-gray fin-accts-number-text"> 
                                                    <template if:true={it.value.bsbLabel}>
                                                        BSB <span class="acc-bsb-label">{it.value.bsbLabel}</span> &nbsp;
                                                    </template>
                                                        ACC <span class="acc-bsb-label">{it.value.accLabel}</span> &nbsp;
                                                </p>
                                            </div>                                            
                                            <div class="slds-col slds-size_6-of-12 consent-details" style="position: relative">
                                                <p class="text-gray">Consent start date: {it.value.StartDate}</p>
                                                <p class="text-gray">Consent end date: {it.value.EndDate}</p>
                                            </div>
                                        </div>
                                        </lightning-layout-item>
                                    </lightning-layout>
                                    <div if:false={it.last} key={it.value.id} class="slds-border_bottom border-style"></div>
                                </div>
                            </template> 
                         <!-- end of SFP-55861-->    
                        </div> 
                    </div>			    
                </template>
                <!-- Key additional information -->
                <h3 class="slds-text-align_left header-spacing font-style_light-bold">Key additional information</h3>
                <div class="content__body-main-dashboard-shadow slds-var-p-around_large slds-border_bottom slds-border_right slds-border_left">
                    <div>
                        <h6>Accreditation</h6>
                        <p class="ds-text-font-style_small text-gray float_none linkTextUnderLine">{recipientName} is an accredited data recipient. You can check their accreditation at <a href={labels.CDR_ACCREDITATION_LINK} target="_blank" class="link-blue ds-text-font-style_small float_none">Consumer Data Right</a>.</p>                        
                        <div class="authorisation-box slds-var-p-around_x-small slds-var-m-vertical_medium slds-m-horizontal_none">
                            <lightning-layout>
                                <lightning-layout-item flexibility="auto" padding="around-none" class="slds-align_absolute-center cdr-logo">
                                    <img src={cdrLogo} class="cdr-logo-style" aria-label="CDR Logo" alt="CDR Logo">
                                </lightning-layout-item>
                                <lightning-layout-item flexibility="auto" padding="around-none" class="slds-text-align_left recipient-info-layout slds-var-p-left_small slds-grid slds-grid_vertical-align-center">
                                    <div>
                                        <h6>{recipientName}</h6>
                                        <div class="authorisation-box-text">
                                            <span class="c-grouped-text">Accredited Data Recipient ID: {recipientEntityId}</span>
                                            <br/><span class="c-grouped-text">Status: {recipientStatus}</span>
                                            <br/><span class="c-grouped-text">Software Product: {softwareProductName}</span>
                                            <br/><span class="c-grouped-text">Software Product Status: {softwareProductStatus}</span>
                                        </div>
                                    </div>
                                </lightning-layout-item>
                            </lightning-layout>
                        </div>
                        <h6>Other important information</h6>
                        <p class="ds-text-font-style_small text-gray float_none">There may be additional important information that is not shown here. Please check this sharing arrangement on the {recipientName} website/app. </p>
                    </div>
                </div>
                <template if:true={active}>
                    <template if:true={isOwnedByLoggedInUser}> <!-- Only the CAR owner can withdraw -->
                        <br/>
                        <h5 class="hurme-text">
                            <button data-id={consentRecordId} type="button" class="slds-button slds-button_neutral button__medium button__stop-sharing" onclick={handleStopSharing}>Stop sharing</button>
                        </h5>
                    </template>
                    <template if:false={isOwnedByLoggedInUser}> <!-- Only the CAR owner can withdraw -->
                        <br/>
                        <h5 class="hurme-text">
                            <button data-id={consentRecordId} type="button" class="slds-button slds-button_neutral button__medium button__stop-sharing" onclick={handleStopSharing}>Stop sharing</button>
                        </h5>
                    </template>
                </template>
            </template>
            <template if:false={isRecordFound}>
                <h2 class="slds-text-align_left header-spacing font-style_light-bold">{errorMessage}</h2>
            </template>
        </template>
    </div>
</template>