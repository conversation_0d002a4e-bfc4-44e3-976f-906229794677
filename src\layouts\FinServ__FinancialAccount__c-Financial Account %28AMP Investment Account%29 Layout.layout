<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Clone</excludeButtons>
    <excludeButtons>Edit</excludeButtons>
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Alternate_Product_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>SCV_PrimaryOwner__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>FinServ__PrimaryOwner__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>POWN_Party_Category__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>JOWN_External_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Commission_Split_v2__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FinServ__FinancialAccountNumber__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>POWN_External_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SCV_JointOwner__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FinServ__JointOwner__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Insurance Details</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Account Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FinServ__OpenDate__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Class_Of_Business_Desc__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Premium_Index_Indicator__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Investment_Direct_Debit_Bank_Account_Det__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Contract_Expiry_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Stamp_Duty_Instalment_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Net_Redemption_Indicator__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Last_Reversionary_Bonus__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Super_Plan_Fee__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Investment_Profile_Description__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Investment_Telephone_Redemption__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Total_Bonus__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Withdrawal_Amount_Text__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Net_Plan_Value_Eftv_Date__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Total_Account_Balance__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Contribution_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Fax_Redemption_Indicator__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Easydraw_Indicator__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Flat_Fee_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Invest_Easy_Indicator__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FinServ__LastUpdated__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Table_Code__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Anniversary_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Normal_Retire_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TFN_Held_Code__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Total_Withdrawal_Fee__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Net_Surrender_Formula__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Plan_Amount_Formula__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>USI_Code__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Portfolio Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Balance_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Term_Deposit_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Listed_Securities_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Cash_Account_Balance__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Managed_Funds_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Portfolio Details - Gross Account Values</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>As_Gross_Account_Value_Total__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>As_Managed_Funds__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Margin_Loan_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bank_Account_Number__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>As_Cash_Account__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>As_Listed_Securities__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Bank_BSB__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Fee Structure</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Deposit_Fee_Pcnt__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Managed_Fund_Fee_Pcnt__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Shares_Fee_Pcnt__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Cash_Fee_Pcnt__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Flat_Fee_Pcnt__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Upfront_Fee_Pcnt__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Margin Lending Service</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Margin_Loan_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Margin_Loan_Amount_Indicator__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Gearing_Ratio__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Reference_Margin_Loan_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Total Death Benefit Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Super_Sum_Insured__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Super_Annual_Bonus__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Super_Premium_Debt__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Additional_Death__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Total_Loan_Debt__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Total_Death_Benefit__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Fee Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Fee_Section_Heading__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Investment_Ongoing_Advice_Fee__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Advice_Ongoing_Fee_Pcnt__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Withdrawal Benefit</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Gross_Withdrawal_Benefit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Loan_Debt__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Premium_Debt__c</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Amount_In_Credit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Withdrawal_Value__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Loan_Balance_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Withdrawal_Value_Eftv_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Net_Withdrawal_Benefit__c</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Issue_Location__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Loan_Interest_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Missed_Payment_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Overdue_Interest__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Contact Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Address_External__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Adviser Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Adviser_Name_Formula__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Customer_Auth_Given_Ind__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Sales_Account__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Superannuation Benefits Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Eligible_Start_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ETP_Fund_Start_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Tax_Free_Calculations__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Preserved_Amount_Calculations__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Unrestrict_Non_Prsrvd_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ETP_Effective_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Taxable_Calculations__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Total_ETP_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Restrict_Non_Prsrvd_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Guarantee Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Guarantee_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Guarantee_Strategy__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Guarantee_Term__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Guarantee_Est_Date__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Policy Value Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Total_Investment_Guaranteed_Component__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SGD_Death_Benefit__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Plan_Value_After_Up__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Payment Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Index_Increase_Indicator__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Payment_Missed_Payment_Amount__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Investment Details</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FinServ__LoanAmount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FinServ__AssetRebalance__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Investment_Category_Code__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FinServ__RebalanceFrequency__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>BPay Biller Codes</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>BPAY_Reference_Number__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Member_BpayBillercode__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Servicing_Adviser__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>RecordTypeId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Product__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Servicing_Practice__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Product_System_Code__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__FinancialAccount__c.New_Case</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__FinancialAccount__c.Create_New_Alert</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewTask</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewTaskAdvisor</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewTaskClientAssociate</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewEventAdvisor</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewEventClientAssociate</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Sales_Comms_Log</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>9</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>10</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__LogACallAdvisor</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>11</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__LogACallClientAssociate</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>12</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__FinancialAccount__c.New_Retention</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>13</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <quickActionList>
        <quickActionListItems>
            <quickActionName>FinServ__FinancialAccount__c.FinServ__NewOpportunity</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FinServ__FinancialAccount__c.FinServ__NewWalletShareOpportunity</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FinServ__FinancialAccount__c.NewComplaint</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FinServ__FinancialAccount__c.Create_New_Alert</quickActionName>
        </quickActionListItems>
    </quickActionList>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Category__c</fields>
        <fields>FinServ__Message__c</fields>
        <fields>Active__c</fields>
        <fields>Start_Date__c</fields>
        <fields>End_Date__c</fields>
        <relatedList>FinServ__Alert__c.FinServ__FinancialAccount__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>SCV_RelatedAccount__c</fields>
        <fields>FinServ__RelatedAccount__c</fields>
        <fields>FinServ__RelatedContact__c</fields>
        <fields>Secondary_User_Instruction__c</fields>
        <fields>Full_Authority__c</fields>
        <fields>FinServ__Role__c</fields>
        <fields>FinServ__Active__c</fields>
        <fields>FinServ__StartDate__c</fields>
        <fields>FinServ__EndDate__c</fields>
        <relatedList>FinServ__FinancialAccountRole__c.FinServ__FinancialAccount__c</relatedList>
        <sortField>FinServ__Active__c</sortField>
        <sortOrder>Desc</sortOrder>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Primary_Policy_Number__c</fields>
        <fields>Payroll_Number__c</fields>
        <fields>Type__c</fields>
        <relatedList>Fin_Account_Fin_Account_Relation__c.Financial_Account_From__c</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedContentNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>FinServ__Revenue__c.FinServ__FinancialAccount__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>FinServ__FinancialHolding__c.FinServ__FinancialAccount__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Financial_Account_Investment_Profile__c.Financial_Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Financial_Account_Payment__c.Financial_Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Financial_Account_Insurance__c.Financial_Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Remuneration_Tier__c.Financial_Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>New</excludeButtons>
        <fields>FinServ__RelatedAccount__c</fields>
        <fields>FinServ__Role__c</fields>
        <fields>Service_Request_case__c</fields>
        <fields>FinServ__Active__c</fields>
        <fields>FinServ__StartDate__c</fields>
        <fields>FinServ__EndDate__c</fields>
        <fields>Access_Level__c</fields>
        <relatedList>FinServ__AccountAccountRelation__c.Financial_Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>New</excludeButtons>
        <fields>FinServ__RelatedContact__c</fields>
        <fields>FinServ__Role__c</fields>
        <fields>Service_Request_case__c</fields>
        <fields>FinServ__Active__c</fields>
        <fields>FinServ__StartDate__c</fields>
        <fields>FinServ__EndDate__c</fields>
        <fields>Access_Level__c</fields>
        <relatedList>FinServ__ContactContactRelation__c.Financial_Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>NewCase</excludeButtons>
        <fields>CASES.CASE_NUMBER</fields>
        <fields>Request_ID__c</fields>
        <fields>CASES.CREATED_DATE</fields>
        <fields>CASES.STATUS</fields>
        <fields>Account_Name__c</fields>
        <fields>CASES.RECORDTYPE</fields>
        <fields>Type__c</fields>
        <fields>Sub_Type__c</fields>
        <fields>CASES.TYPE</fields>
        <fields>Category__c</fields>
        <relatedList>Case.Financial_Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Flexible_Bonus_Group_Number__c</fields>
        <fields>Flexible_Bonus_Group_Type__c</fields>
        <fields>Date_Included__c</fields>
        <fields>Date_Removed__c</fields>
        <fields>Relationship_Status__c</fields>
        <fields>NAME</fields>
        <relatedList>Flexible_Bonus_Relationship__c.Financial_Account__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Disposing_Sales_ID__c</fields>
        <fields>Acquiring_Sales_ID__c</fields>
        <fields>CREATED_DATE</fields>
        <relatedList>Financial_Account_Transfer__c.Financial_Account__c</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>true</showHighlightsPanel>
    <showInteractionLogPanel>true</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h2P000000MoTr</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
