<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>invocableContextErrorEmail</name>
        <label>Context Error Email</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <stringValue>Your Process Failed to Execute</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <stringValue>The eCRM_Case_L2_Complaint_Actionable-1 invocable process failed to start because no records in your org match the record Id specified. In the entities that invoke eCRM_Case_L2_Complaint_Actionable-1, such as other processes, check the value for this input: SObjectId.</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
    </actionCalls>
    <actionCalls>
        <name>invocableSObjectErrorEmail</name>
        <label>SObject Or Id Error Email</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <stringValue>Your Process Failed to Execute</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <stringValue>The eCRM_Case_L2_Complaint_Actionable-1 invocable process failed to start because a valid value wasn&apos;t found. This can happen when entities that invoke eCRM_Case_L2_Complaint_Actionable-1, such as other processes, don&apos;t have values for either the SObject or SObjectId. For example: 
-An SObject or SObject ID wasn&apos;t specified.
-A value might have been included, but it was null at runtime. </stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
    </actionCalls>
    <actionCalls>
        <processMetadataValues>
            <name>emailAlertSelection</name>
            <value>
                <stringValue>Email_alert_for_escalation_of_case_complaint</stringValue>
            </value>
        </processMetadataValues>
        <name>myRule_1_A1</name>
        <label>Send Escalation Email Alert</label>
        <locationX>100</locationX>
        <locationY>200</locationY>
        <actionName>Case.Email_alert_for_escalation_of_case_complaint</actionName>
        <actionType>emailAlert</actionType>
        <inputParameters>
            <name>SObjectRowId</name>
            <value>
                <elementReference>SObject.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>Case.Email_alert_for_escalation_of_case_complaint</nameSegment>
    </actionCalls>
    <apiVersion>49.0</apiVersion>
    <decisions>
        <name>invocableContextCheckDecision</name>
        <label>Context Check Decision</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>invocableContextErrorEmail</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>invocableContextCheckRule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>SObject</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myDecision</targetReference>
            </connector>
            <label>SObject Context Record Found</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision</name>
        <label>myDecision</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>myDecision2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>SObject.IsEscalated</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myRule_1_A1</targetReference>
            </connector>
            <label>Case is Escalated</label>
        </rules>
    </decisions>
    <decisions>
        <processMetadataValues>
            <name>index</name>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </processMetadataValues>
        <name>myDecision2</name>
        <label>myDecision2</label>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>myRule_3</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>Date</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>Date</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>Boolean</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>SObject.Diarised_Until__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <processMetadataValues>
                    <name>inputDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>leftHandSideType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>operatorDataType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <processMetadataValues>
                    <name>rightHandSideType</name>
                    <value>
                        <stringValue>String</stringValue>
                    </value>
                </processMetadataValues>
                <leftValueReference>SObject.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Adviser_Complaint</stringValue>
                </rightValue>
            </conditions>
            <label>Complaint is On Hold</label>
        </rules>
    </decisions>
    <decisions>
        <name>sobjectInputCheckDecision</name>
        <label>SObject Input Check Decision</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>invocableSObjectErrorEmail</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>sobjectInputCheckRule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>SObject</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myDecision</targetReference>
            </connector>
            <label>SObject Not Null</label>
        </rules>
        <rules>
            <name>sobjectIdInputCheckRule</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>SObjectId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>myInvocableContextRecordLookup</targetReference>
            </connector>
            <label>SObject Id Not Null</label>
        </rules>
    </decisions>
    <interviewLabel>eCRM_Case_L2_Complaint_Actionable-1_InterviewLabel</interviewLabel>
    <label>eCRM_Case_L2_Complaint_Actionable</label>
    <processMetadataValues>
        <name>ObjectType</name>
        <value>
            <stringValue>Case</stringValue>
        </value>
    </processMetadataValues>
    <processType>InvocableProcess</processType>
    <recordLookups>
        <name>myInvocableContextRecordLookup</name>
        <label>myInvocableContextRecordLookup</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>invocableContextCheckDecision</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>SObjectId</elementReference>
            </value>
        </filters>
        <object>Case</object>
        <outputReference>SObject</outputReference>
        <queriedFields>IsEscalated</queriedFields>
        <queriedFields>Diarised_Until__c</queriedFields>
        <queriedFields>RecordTypeId</queriedFields>
    </recordLookups>
    <startElementReference>sobjectInputCheckDecision</startElementReference>
    <status>Obsolete</status>
    <variables>
        <name>SObject</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>SObjectId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
