<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>Accept</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <allowInChatterGroups>false</allowInChatterGroups>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <deploymentStatus>Deployed</deploymentStatus>
    <description>Object to store the answers for the client file check questionnaire so that it may be saved from the modal.</description>
    <enableActivities>false</enableActivities>
    <enableBulkApi>true</enableBulkApi>
    <enableFeeds>false</enableFeeds>
    <enableHistory>false</enableHistory>
    <enableLicensing>false</enableLicensing>
    <enableReports>false</enableReports>
    <enableSearch>true</enableSearch>
    <enableSharing>true</enableSharing>
    <enableStreamingApi>true</enableStreamingApi>
    <externalSharingModel>ReadWrite</externalSharingModel>
    <fields>
        <fullName>Answer__c</fullName>
        <description>Stored answer(s) for the question</description>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Answer</label>
        <length>255</length>
        <required>false</required>
        <trackTrending>false</trackTrending>
        <type>Text</type>
        <unique>false</unique>
    </fields>
    <fields>
        <fullName>Data_Type__c</fullName>
        <description>Data type of the available answers for the question</description>
        <externalId>false</externalId>
        <label>Data Type</label>
        <required>false</required>
        <trackTrending>false</trackTrending>
        <type>Picklist</type>
        <valueSet>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value>
                    <fullName>Picklist</fullName>
                    <default>false</default>
                    <label>Picklist</label>
                </value>
                <value>
                    <fullName>Picklist (Multi-Select)</fullName>
                    <default>false</default>
                    <label>Picklist (Multi-Select)</label>
                </value>
                <value>
                    <fullName>Text</fullName>
                    <default>false</default>
                    <label>Text</label>
                </value>
                <value>
                    <fullName>Number</fullName>
                    <default>false</default>
                    <label>Number</label>
                </value>
                <value>
                    <fullName>Checkbox</fullName>
                    <default>false</default>
                    <label>Checkbox</label>
                </value>
                <value>
                    <fullName>Currency</fullName>
                    <default>false</default>
                    <label>Currency</label>
                </value>
                <value>
                    <fullName>Date</fullName>
                    <default>false</default>
                    <label>Date</label>
                </value>
            </valueSetDefinition>
        </valueSet>
    </fields>
    <fields>
        <fullName>Opportunity__c</fullName>
        <deleteConstraint>SetNull</deleteConstraint>
        <externalId>false</externalId>
        <label>Opportunity</label>
        <referenceTo>Opportunity</referenceTo>
        <relationshipLabel>Checklist Questionnaires</relationshipLabel>
        <relationshipName>Checklist_Questionnaires</relationshipName>
        <required>false</required>
        <trackTrending>false</trackTrending>
        <type>Lookup</type>
    </fields>
    <fields>
        <fullName>Options__c</fullName>
        <description>Options for picklist type or multi-select picklist separated by semi-colon</description>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <inlineHelpText>Options for picklist type or multi-select picklist separated by semi-colon</inlineHelpText>
        <label>Options</label>
        <length>32768</length>
        <trackTrending>false</trackTrending>
        <type>LongTextArea</type>
        <visibleLines>3</visibleLines>
    </fields>
    <fields>
        <fullName>Question_Label__c</fullName>
        <description>Question from the questionnaire</description>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Question Label</label>
        <length>131072</length>
        <trackTrending>false</trackTrending>
        <type>LongTextArea</type>
        <visibleLines>3</visibleLines>
    </fields>
    <fields>
        <fullName>Required_Document__c</fullName>
        <description>Stores all required documents types for the question</description>
        <encryptionScheme>None</encryptionScheme>
        <externalId>false</externalId>
        <label>Required Document</label>
        <length>32768</length>
        <trackTrending>false</trackTrending>
        <type>LongTextArea</type>
        <visibleLines>3</visibleLines>
    </fields>
    <label>Checklist Questionnaire</label>
    <listViews>
        <fullName>All</fullName>
        <filterScope>Everything</filterScope>
        <label>All</label>
    </listViews>
    <nameField>
        <displayFormat>Q-{00000}</displayFormat>
        <label>Checklist Questionnaire Name</label>
        <type>AutoNumber</type>
    </nameField>
    <pluralLabel>Checklist Questionnaires</pluralLabel>
    <searchLayouts/>
    <sharingModel>ReadWrite</sharingModel>
    <visibility>Public</visibility>
</CustomObject>
