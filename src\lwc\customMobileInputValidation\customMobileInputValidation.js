/* 
* SFP-30086 by <PERSON><PERSON><PERSON> (Accenture)
* JS controller for CustomMobileInputValidation Component
*/
import { LightningElement, track, wire, api } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import getMobileWebServiceInterface from '@salesforce/apex/ContactDetailsSectionController.getMobileWebServiceInterface';
import getVerificationDetailsForIdCheck from '@salesforce/apex/ContactDetailsSectionController.getVerificationDetailsForIdCheck';
import updateVerificationDetailsForIdCheckMobile from '@salesforce/apex/ContactDetailsSectionController.updateVerificationDetailsForIdCheck';
import hasMobileChangedInPastMonths from '@salesforce/apex/ContactDetailsSectionController.hasMobileChangedInPastMonths';
import CONTACT_MOBILE_HELPTEXT from '@salesforce/label/c.ContactEmailAndMobileHelpText';
import hasCustomPermission from '@salesforce/customPermission/New_Zealand_User';
import hasSend2FAPermission from '@salesforce/customPermission/Send_Customer_2FA_SMS';

export default class CustomMobileInputValidation extends LightningElement {
    @track mobileInputClass;
    @track mobileVerifySwitch = false;
       
    @api recordId;
    @api objectApiName;
    @api mobileval;
    @api isEditMode;
    @api disableMobileVerifyButton;
    @api isIdCheckScreen;
    @api recordIdFromFlow;
    @api mobileFromFlow;
    @api hasMobileMessage;
    @api mobileInvalidFromFlow; // SFP-39722
    @api 
    get editAccess(){
        return this._editAccess;
    }
    set editAccess(value) {
        this._editAccess = value;

         // SFP-42866 by J.Mendoza 
        // transferred web service interface details in getter, setter instead of using wire
        // SFP-35634 by J.Mendoza
        // Set Verify button visibilty on Email field
        getMobileWebServiceInterface()
        .then(result=>{
            this.isMobileVerifyButtonVisible = false;
            let globalSwitch = result['globalSwitch'];
            let experianSwitch = result['experianSwitch'];
            
            if(globalSwitch){       // if global switch is disabled
                this.mobileVerifySwitch = false;
            } else {                // else global switch is enabled
                if(experianSwitch){ // if experian switch is disabled
                    this.mobileVerifySwitch = false;
                } else {            // else experian switch is enabled
                    this.mobileVerifySwitch = true;
                }
            }
                
            //SFP-32688 by Heither Ann Ballero
            //If the Email is populated AND is not edit mode AND user has edit acess AND experian switch is enabled, show the Verify button
            if(this.mobileval && !this.isEditMode && this.editAccess && this.mobileVerifySwitch){
                this.isMobileVerifyButtonVisible = true;
                this.mobileInputClass = 'slds-size_9-of-12 slds-col slds-p-left_none slds-p-right_medium';
            } else{
                this.isMobileVerifyButtonVisible = false;
                this.mobileInputClass = 'slds-size_11-of-12 slds-col slds-p-left_none slds-p-right_medium';
            }
        }).catch(error=>{
            // SFP-38148 by Heither Ann Ballero
            // Commented the console.log and replace it with an error toast
            // console.log('Error from ContactDetailsSectionController.getEmailWebServiceInterface: ', error);
            let event = new ShowToastEvent({
                title: 'Error!',
                message: 'An error has occurred.',
                variant: 'error',
                mode: 'dismissable'
            });
            this.dispatchEvent(event);
        });
    }

    isMobileVerifyButtonVisible;
    isMobileVerifyButtonForIdCheckVisible = false;
    processing = false;
    disableEditBtn = false;
    newMobileValue = '';
    error;
    statusCode;
    originalMobile;
    mobileEdited = false;
    cameFromOnchange = false;
    validMobile = false;
    fromVerifyButton = false;
    mobileChangedInPast3Months = false;

    // Modal properties
    isModalOpen = false;
    verificationCodeSent = false;
    verificationCode = '';

    //message details
    verificationMessage;
    verificationIconName;
    verificationVariant;
    verificationStyle;
    verificationMobile;
    dateDetails;
    verificationStatus;
    timeStampForMobile;
    spinnerContainerSize;
    helpText = CONTACT_MOBILE_HELPTEXT;

    @api isCallCenter = false;

    // SFP-36333 by Heither Ann Ballero
    // Exclude NZ users from verification
    get isNZUser() {
        return hasCustomPermission;
    }

    // Check if user has permission to send 2FA SMS (for visibility)
    get canSend2FASMS() {
        return hasSend2FAPermission;
    }

    // Check if Send 2FA SMS button should be disabled
    get isSend2FADisabled() {
        return this.mobileChangedInPast3Months;
    }

    // SFP-35634 by J.Mendoza
    // Set Verify button visibilty on Email field
    @wire(getMobileWebServiceInterface)
    wiredMobileWebServiceInterface({error, data}){
        if(this.isIdCheckScreen){
            if(data){
                this.isMobileVerifyButtonVisible = false;
                let globalSwitch = data['globalSwitch'];
                let experianSwitch = data['experianSwitch'];
            
                if(globalSwitch){       // if global switch is disabled
                    this.mobileVerifySwitch = false;
                } else {                // else global switch is enabled
                    if(experianSwitch){ // if experian switch is disabled
                        this.mobileVerifySwitch = false;
                    } else {            // else experian switch is enabled
                        this.mobileVerifySwitch = true;
                    }
                }
                
            } else if (error){
                // SFP-38148 by Heither Ann Ballero
                // Commented the console.log and replace it with an error toast
                // console.log('Error from ContactDetailsSectionController.getMobileWebServiceInterface: ', error);
                let event = new ShowToastEvent({
                    title: 'Error!',
                    message: 'An error has occurred.',
                    variant: 'error',
                    mode: 'dismissable'
                });
                this.dispatchEvent(event);
            }
        }
        
    }

    //SFP-36490 by Heither Ann Ballero
    connectedCallback(){
        let objectApiName = this.objectApiName;
        let mobileApiName;
        //if individual account
        if(objectApiName === 'Contact'){
            mobileApiName = 'MobilePhone';
        }
        //if organisation account
        else{
            mobileApiName = 'Mobile__c';
        }
        this.mobileApiName = mobileApiName;

        // Check if mobile has changed in past 3 months
        if(this.recordId){
            this.checkMobileChangeHistory(this.recordId);
        }

        //SFP-32918 by Heither Ann Ballero
        //Set the button to visible when the mobile field is populated and the mobileVerifySwitch is true
        if(this.isIdCheckScreen){
            if(this.mobileFromFlow && this.mobileVerifySwitch && !this.mobileInvalidFromFlow){
                this.isMobileVerifyButtonForIdCheckVisible = true;
            } 
            else {
                this.isMobileVerifyButtonForIdCheckVisible = false;
            }

            // Check mobile change history for ID check flow
            if(this.recordIdFromFlow){
                this.checkMobileChangeHistory(this.recordIdFromFlow);
            }

            //Get the existing verification details
            getVerificationDetailsForIdCheck({
                recordId : this.recordIdFromFlow
            }).then(result=>{
                this.disableEditBtn = true;
                let iconName = '';
                let variant = '';
                let eStyle = '';
                let css = 'slds-text-body--small slds-p-right_small slds-p-top_xx-small slds-p-bottom_x-small ';
                switch(result.mobileValidationStatus){
                    case 'Verified':
                        iconName = 'utility:success';
                        variant = 'success';
                        eStyle = css+ 'verification-message_verified';
                        break;
                    case 'Unverified':
                    case 'Teleservice not provisioned':
                    case 'No coverage':
                    case 'Dead':    
                        iconName = 'utility:clear';
                        variant = 'error';
                        eStyle = css+ 'verification-message_error';
                        break;
                    case 'Unknown':
                    case 'Absent':
                        iconName = 'utility:success';
                        variant = 'brand';
                        eStyle = css+ 'verification-message_bare';
                        break;
                    case 'Unavailable':
                        iconName = 'utility:question';
                        variant = 'brand';
                        eStyle = css+ 'verification-message_bare'
                        break;
                    case 'Switched off':
                        iconName = 'utility:question';
                        variant = 'brand';
                        eStyle = css + 'verification-message_bare';
                        break;
                }
                
                this.verificationMessage = result.mobileValidationMessage;
                this.verificationStatus = result.mobileValidationStatus;
                let date = new Date(result.mobileValidationTimestamp);
                let formattedDate = date.toLocaleDateString('en-GB', { day: 'numeric', month: 'numeric', year: 'numeric' }).replace(/ /g, '/');
                this.dateDetails = ' (as at ' +formattedDate+ ')';
                this.verificationIconName = iconName;
                this.verificationStyle = eStyle;
                this.verificationVariant = variant;

                //SFP-32918 by Heither Ann Ballero
                //If the mobile was last validated for more than 24 months, or result was unavailable or switched off, or there was no prior validation to the mobile, enable button
                if(result.monthsInBetweenLastValidationForMobile > 24 || result.mobileValidationStatus == 'Unavailable' || result.mobileValidationStatus == 'Switched off' || (!result.mobileValidationStatus && !result.mobileValidationMessage)){
                    this.disableMobileVerifyButton = false;
                }else{
                    this.disableMobileVerifyButton = true;
                }
            }).catch(error=>{
                // console.log('Error ', error);
                let event = new ShowToastEvent({
                    title: 'Error!',
                    message: 'An error has occurred.',
                    variant: 'error',
                    mode: 'dismissable'
                });
                this.dispatchEvent(event);
            });
        }
    }

    checkMobileChangeHistory(recordId){
        if(recordId){
            hasMobileChangedInPastMonths({
                recordId: recordId,
                monthsBack: 3
            })
            .then(result => {
                this.mobileChangedInPast3Months = result;
            })
            .catch(error => {
                console.log('Error checking mobile change history:', error);
                this.mobileChangedInPast3Months = false;
            });
        }
    }

    //onblur event to get verification response
    verifyNewMobileOnBlur(event){
        this.getVerificationResponse(event);
    }

    //SFP-32688 by Heither Ann Ballero
    verifyMobile(event){
        this.fromVerifyButton = true;
        this.getVerificationResponse(event);
    }

    handleSend2FASMS(event){
        // Check if user has permission to send 2FA SMS
        if (!hasSend2FAPermission) {
            let errorEvent = new ShowToastEvent({
                title: 'Access Denied',
                message: 'You do not have permission to send 2FA SMS.',
                variant: 'error',
                mode: 'dismissable'
            });
            this.dispatchEvent(errorEvent);
            return;
        }

        // Check if mobile has changed in past 3 months
        if (this.mobileChangedInPast3Months) {
            let errorEvent = new ShowToastEvent({
                title: '2FA SMS Disabled',
                message: 'Cannot send 2FA SMS. Mobile number has been changed in the past 3 months.',
                variant: 'warning',
                mode: 'dismissable'
            });
            this.dispatchEvent(errorEvent);
            return;
        }

        // Open the modal
        this.isModalOpen = true;
    }

    // Method to close the modal
    closeModal() {
        this.isModalOpen = false;
        this.verificationCodeSent = false;
        this.verificationCode = '';
    }

    // Method to handle sending verification code
    handleSendVerificationCodeAction(event) {
        // Show toast message indicating verification code is being sent
        let verificationEvent = new ShowToastEvent({
            title: 'Verification Code',
            message: 'A verification code is being sent to ' + this.mobileval + '. It may take a few minutes to arrive.',
            variant: 'info',
            mode: 'dismissable'
        });
        this.dispatchEvent(verificationEvent);

        // Set the state to show verification code input field
        this.verificationCodeSent = true;

        // TODO: This will be replaced with actual LWC component logic
        console.log('Verification code sent to mobile:', this.mobileval);
    }

    // Method to handle verification code input
    handleVerificationCodeChange(event) {
        this.verificationCode = event.target.value;
    }

    // Method to check the verification code
    handleCheckVerificationCode(event) {
        // TODO: Add actual verification logic here
        console.log('Checking verification code:', this.verificationCode);

        // For now, just show a placeholder toast
        let checkEvent = new ShowToastEvent({
            title: 'Verification',
            message: 'Checking verification code: ' + this.verificationCode,
            variant: 'info',
            mode: 'dismissable'
        });
        this.dispatchEvent(checkEvent);
    }

    //SFP-32918 by Heither Ann Ballero
    //Update the verification fields based from the result of the callout
    updateVerificationDetailsForIdCheckMobile(){
        this.processing = true;
        if(this.verificationStatus == 'Unavailable'){
            this.disableMobileVerifyButton = false;
        }
        updateVerificationDetailsForIdCheckMobile({
            recordId : this.recordIdFromFlow,
            message : this.verificationMessage,
            status : this.verificationStatus,
            isEmail : false
        }).then(result=>{
            this.processing = false;
        })
        .catch(error=>{
            //if failed, remove overlay and enable Verify button
            this.disableMobileVerifyButton = false;
            this.processing = false;
            
            // console.log('Error in verifying mobile ', error);
            let event = new ShowToastEvent({
                title: 'Error!',
                message: 'An error has occurred.',
                variant: 'error',
                mode: 'dismissable'
            });
            this.dispatchEvent(event);
        });
    }

    //get experian verification response
    getVerificationResponse(event){
        let newMobile = this.newMobileValue;
        let originalMobile = this.originalMobile;
        let inputValid = this.validMobile;
        let isMobileEdited = this.mobileEdited;
        let css = 'slds-text-body--small slds-p-right_small slds-p-top_xx-small slds-p-bottom_x-small ';
        let message = '';
        let eStyle = '';
        let iconName = '';
        let variant = '';
        let status;
        const date = new Date();
        let todayDate = date.toLocaleDateString('en-GB', { day: 'numeric', month: 'numeric', year: 'numeric' }).replace(/ /g, '/'); // convert to dd/mm/yyyy format
        this.timeStampForMobile  = date.toISOString(); //convert to datetime format to be stored in sf field

        if(!this.hasMobileMessage){
            this.spinnerContainerSize = 'slds-spinner_container'
        }
        else if(this.hasMobileMessage){
            if(this.fromVerifyButton){
                this.spinnerContainerSize = 'slds-spinner_container spinner-container-size_verify';
            }
            else{
                this.spinnerContainerSize = 'slds-spinner_container spinner-container-size_edit';
            }
        }
        
        getMobileWebServiceInterface()
        .then(result => {
            // SFP-33299 added by J.Mendoza
            // check if global switch isn't disabled
            if(result['globalSwitch']==false){ 
                //SFP-32918 by Heither Ann Ballero
                //If from ID check, use the mobile value from the flow
                if(this.isIdCheckScreen){
                    this.mobileval = this.mobileFromFlow;
                }
                //only request callout when edited mobile was valid and not empty or when Verify button is clicked
                if((inputValid && newMobile!='' && isMobileEdited && this.cameFromOnchange && !this.isNZUser) || this.fromVerifyButton){
                    this.processing = true;

                    // SFP-36950 by J.Mendoza (Accenture) 07/28/20
                    // replace starting mobile numbers for AU and NZ with proper country codes
                    let mobile = (this.mobileval).replace(/[+() ]/g, '');
                    let mobileNumber = this.mobileval;
                    if((this.mobileval).startsWith('04') && (mobile).length == 10){ // AU
                        mobileNumber = (mobileNumber).replace('04','614');
                    } else if(this.mobileval.startsWith('02') && mobile.length <= 11 && mobile.length >= 9){ // NZ
                        mobileNumber = (mobileNumber).replace('02','642');
                    }

                    const url = result['url'];
                    const params = {
                        method: 'POST',
                        headers: {
                        "Content-Type" : "application/json",
                        "Auth-Token": result['authToken'],
                        "Timeout-Seconds" : result['timeout']
                        },
                        body: '{"number":"'+ mobileNumber+'"}'
                    };
        
                    fetch(url,params)
                    .then((response) => {
                        this.statusCode = response.status;
                        console.log('.statusCode', this.statusCode);
                        return response.json(); // returning the response in the form of JSON
                    })
                    .then((jsonResponse) => {
                        window.console.log('jsonResponse'+JSON.stringify(jsonResponse));
                        let objData = {
                            resultConfidence : '',
                        };

                        if(this.statusCode == 200 && jsonResponse['result']){
                            // retriving the response data
                            let exchangeData = jsonResponse['result'];
            
                            // adding data object
                            objData.resultConfidence = exchangeData['confidence'];
                            if(objData.resultConfidence){
                                //get response message and corresponding icons, styling and status
                                switch(objData.resultConfidence){
                                    case 'Verified':
                                        message = 'Successfully verified as reachable';
                                        iconName = 'utility:success';
                                        variant = 'success';
                                        eStyle = css+ 'verification-message_verified';
                                        status = 'Verified';
                                        break;
                                    case 'Unverified':
                                        message = 'Mobile is unreachable';
                                        iconName = 'utility:clear';
                                        variant = 'error';
                                        eStyle = css+ 'verification-message_error';
                                        status = 'Unverified';
                                        break;
                                    case 'Unknown':
                                        message = 'Unable to verify mobile but it may be correct';
                                        iconName = 'utility:success';
                                        variant = 'brand';
                                        eStyle = css+'verification-message_bare';
                                        status = 'Unknown';
                                        break;
                                    case 'Absent':
                                        message = 'Successfully verified but not currently reachable';
                                        iconName = 'utility:success';
                                        variant = 'brand';
                                        eStyle = css+'verification-message_bare';
                                        status = 'Absent';
                                        break;
                                    case 'Teleservice not provisioned':
                                        message = 'Mobile is unreachable';
                                        iconName = 'utility:clear';
                                        variant = 'error';
                                        eStyle = css+'verification-message_error';
                                        status = 'Teleservice not provisioned';
                                        break;
                                    case 'No coverage':
                                        message = 'Mobile is unreachable';
                                        iconName = 'utility:clear';
                                        variant = 'error';
                                        eStyle = css+'verification-message_error';
                                        status = 'No coverage';
                                        break;
                                    case 'Dead':
                                        message = 'Mobile is unreachable';
                                        iconName = 'utility:clear';
                                        variant = 'error';
                                        eStyle = css+'verification-message_error';
                                        status = 'Dead';
                                        break;
                                }
                                
                                this.verificationMessage = message;
                                this.verificationIconName = iconName;
                                this.verificationVariant = variant;
                                this.verificationStyle = eStyle;
                                this.verificationMobile = this.fromVerifyButton? this.mobileval : newMobile;
                                this.dateDetails = ' (as at ' +todayDate+ ')';
                                this.verificationStatus = status;
                                this.verificationEventHandler(event);     
                                this.disableMobileVerifyButton = true;   

                                //update details if from id check flow
                                if(this.isIdCheckScreen){
                                    this.updateVerificationDetailsForIdCheckMobile();
                                }
                            }
                        } else {
                            //when server is down or response is unsuccessful
                            // console.log('Response error with status code: ', this.statusCode);
                            this.verificationMessage = 'Verification Service unavailable';
                            this.verificationIconName = 'utility:question';
                            this.verificationVariant = 'brand';
                            this.verificationStyle = css + 'verification-message_bare';
                            this.verificationMobile = newMobile;
                            this.dateDetails = ' (as at ' +todayDate+ ')';
                            this.verificationStatus = 'Unavailable';
                            this.verificationEventHandler(event);

                            //update details if from id check flow
                            if(this.isIdCheckScreen){
                                this.updateVerificationDetailsForIdCheckMobile();
                            }
                        }
                        this.processing = false;
                    })
                    .catch(error => {
                        this.processing = false;
                        // window.console.log('callout error JSON ===> '+error);
                    })
                    this.newMobileValue = '';
                }
                else{
                    //When Mobile was not edited, empty or invalid
                    let mobileValueEvent = this.cameFromOnchange ? newMobile : originalMobile; //this.mobileval;
                    this.verificationMobile = mobileValueEvent;
                    this.verificationEventHandler(event);
                    this.processing = false;
                }
            }
            // else global switch is disabled
            else {
                this.verificationMessage = 'Verification Service is switched off';
                this.verificationIconName = 'utility:question';
                this.verificationVariant = 'brand';
                this.verificationStyle = css + 'verification-message_bare';
                this.verificationMobile = newMobile;
                this.dateDetails = ' (as at ' +todayDate+ ')';
                this.verificationStatus = 'Switched off';
                this.verificationEventHandler(event);
            }
        })
        .catch(error => {
            // console.log('Error ', error);
            let event = new ShowToastEvent({
                title: 'Error!',
                variant: 'error',
                mode: 'dismissable'
            });
            this.dispatchEvent(event);

        });
        
    }

    //Method to handle passing of event
    verificationEventHandler(event){
        let verificationMessage;
        let verificationIconName;
        let verificationVariant;
        let verificationStyle;
        let status;
        let verificationDateDetails;
        let timeStampForMobile;
        let verificationMobile = this.verificationMobile;
        //check if mobile is valid
        if(this.validMobile  || this.fromVerifyButton){
            //check if mobile is edited and not empty or when the Verify button is clicked
            if((this.mobileEdited && verificationMobile != '') || this.fromVerifyButton){
                verificationMessage = this.verificationMessage;
                verificationIconName = this.verificationIconName;
                verificationVariant = this.verificationVariant;
                verificationStyle = this.verificationStyle;
                status = this.verificationStatus;
                verificationDateDetails = this.dateDetails;
                timeStampForMobile = this.timeStampForMobile;
            }
        }

        // Creates the event with the data.
        const verificationEvent = new CustomEvent("changehandle", { 
            detail: {
                mobile : verificationMobile,
                message : verificationMessage,
                iconName : verificationIconName,
                variant : verificationVariant,
                style : verificationStyle,
                isMobileValid : this.validMobile,
                dateDetails : verificationDateDetails,
                status : status,
                timeStampForMobile : timeStampForMobile,
                isMobileEdited: this.mobileEdited,
                fromVerifyButton: this.fromVerifyButton
            }
        });
        this.dispatchEvent(verificationEvent);
        this.processing = false;
    }

    //method to handle when typing to change mobile
    mobileOnchange(event) {
        this.cameFromOnchange = true;
        let mobileInput = this.template.querySelectorAll("lightning-input");
        let newMobileValue;
        let allowedvalues = /^[0-9()+ ]+$/g;
        let spaces;
        let mobile;
        let validMobile = false;
        mobileInput.forEach(function(element){
            if(element.name == "mobile") {
                newMobileValue = element.value;
                mobile = newMobileValue.replace(/[+() ]/g, '');
                spaces = newMobileValue.split(" ").length - 1;
                //Check if the mobile phone value contains allowed values, has 6 or more digits and if the length is less than or equal to 40
                if(newMobileValue.match(allowedvalues) && mobile.length >= 6 && spaces <=1 && newMobileValue.length <=40) {                   
                    validMobile = true;
                    element.setCustomValidity("");
                } else{
                    element.setCustomValidity("Invalid mobile number.");
                }
            }
        },this);

        this.newMobileValue = newMobileValue;
        this.mobileEdited = this.originalMobile !== this.newMobileValue; //check whether the mobile was edited
        this.validMobile = validMobile;
        this.mobileval = newMobileValue;
    }

    //pass event when button is clicked to allow editing whole form
    enableEdit(event){
        const editEvent = new CustomEvent('editevent', {});
        this.dispatchEvent(editEvent);
    }

    //check whether mobile input field is focused on
    onfocusEvent(event){
        this.cameFromOnchange = false;
        this.newMobileValue = this.mobileval;
        this.originalMobile = this.mobileval; //store original mobile in this property to be used for checking if it was edited
        const disableSaveButtonEvent = new CustomEvent('disablesaveevent',{});
        this.dispatchEvent(disableSaveButtonEvent);
    }

}